[{"C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\login\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\AppSidebar.tsx": "4", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "5", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\SupabaseSignIn.tsx": "6", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Button.tsx": "7", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-183.tsx": "8", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-356.tsx": "9", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-581.tsx": "10", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\debug\\SupabaseDebug.tsx": "11", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\info-menu.tsx": "12", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\layout\\ConditionalLayout.tsx": "13", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\LoginDemo.tsx": "14", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\logo.tsx": "15", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Navbar.tsx": "16", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\notification-menu.tsx": "17", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\PortfolioBalance.tsx": "18", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ThemeToggle.tsx": "19", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar-upload.tsx": "20", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar.tsx": "21", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\button.tsx": "22", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\dropdown-menu.tsx": "23", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\label.tsx": "24", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\navigation-menu.tsx": "25", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\popover.tsx": "26", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\sign-in-flow-1.tsx": "27", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\switch.tsx": "28", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\tooltip.tsx": "29", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\user-menu.tsx": "30", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\ProfileContext.tsx": "31", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\SidebarContext.tsx": "32", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useAuth.ts": "33", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useLocalStorage.ts": "34", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useProfile.ts": "35", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useTheme.ts": "36", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\config.ts": "37", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\supabase.ts": "38", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\utils.ts": "39", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\types\\index.ts": "40", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\utils\\index.ts": "41", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\analytics\\page.tsx": "42", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\dashboard\\page.tsx": "43", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\profile\\page.tsx": "44", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\calculator.tsx": "45", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\positions-header.tsx": "46", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\positions-table.tsx": "47", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\positions.tsx": "48", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\select.tsx": "49"}, {"size": 1271, "mtime": 1753430644195, "results": "50", "hashOfConfig": "51"}, {"size": 223, "mtime": 1753428522935, "results": "52", "hashOfConfig": "51"}, {"size": 102, "mtime": 1753427531887, "results": "53", "hashOfConfig": "51"}, {"size": 16647, "mtime": 1753438949298, "results": "54", "hashOfConfig": "51"}, {"size": 1089, "mtime": 1753429458940, "results": "55", "hashOfConfig": "51"}, {"size": 31531, "mtime": 1753463554379, "results": "56", "hashOfConfig": "51"}, {"size": 1917, "mtime": 1753418806563, "results": "57", "hashOfConfig": "51"}, {"size": 1492, "mtime": 1753420281523, "results": "58", "hashOfConfig": "51"}, {"size": 594, "mtime": 1753424177462, "results": "59", "hashOfConfig": "51"}, {"size": 4444, "mtime": 1753419151447, "results": "60", "hashOfConfig": "51"}, {"size": 0, "mtime": 1753433329752, "results": "61", "hashOfConfig": "51"}, {"size": 2012, "mtime": 1753429650737, "results": "62", "hashOfConfig": "51"}, {"size": 2616, "mtime": 1753433736749, "results": "63", "hashOfConfig": "51"}, {"size": 242, "mtime": 1753427369704, "results": "64", "hashOfConfig": "51"}, {"size": 293, "mtime": 1753420738208, "results": "65", "hashOfConfig": "51"}, {"size": 1363, "mtime": 1753438821653, "results": "66", "hashOfConfig": "51"}, {"size": 4733, "mtime": 1753429671725, "results": "67", "hashOfConfig": "51"}, {"size": 6097, "mtime": 1753457316734, "results": "68", "hashOfConfig": "51"}, {"size": 1589, "mtime": 1753434084613, "results": "69", "hashOfConfig": "51"}, {"size": 5404, "mtime": 1753457177590, "results": "70", "hashOfConfig": "51"}, {"size": 1109, "mtime": 1753419126418, "results": "71", "hashOfConfig": "51"}, {"size": 1867, "mtime": 1753419126431, "results": "72", "hashOfConfig": "51"}, {"size": 9326, "mtime": 1753419126465, "results": "73", "hashOfConfig": "51"}, {"size": 595, "mtime": 1753420281548, "results": "74", "hashOfConfig": "51"}, {"size": 6606, "mtime": 1753419126498, "results": "75", "hashOfConfig": "51"}, {"size": 1827, "mtime": 1753419126503, "results": "76", "hashOfConfig": "51"}, {"size": 32007, "mtime": 1753457299287, "results": "77", "hashOfConfig": "51"}, {"size": 1036, "mtime": 1753420281543, "results": "78", "hashOfConfig": "51"}, {"size": 1891, "mtime": 1753424177494, "results": "79", "hashOfConfig": "51"}, {"size": 6583, "mtime": 1753438885270, "results": "80", "hashOfConfig": "51"}, {"size": 9339, "mtime": 1753459776385, "results": "81", "hashOfConfig": "51"}, {"size": 1580, "mtime": 1753425131816, "results": "82", "hashOfConfig": "51"}, {"size": 4175, "mtime": 1753463477029, "results": "83", "hashOfConfig": "51"}, {"size": 1081, "mtime": 1753455879431, "results": "84", "hashOfConfig": "51"}, {"size": 214, "mtime": 1753433704921, "results": "85", "hashOfConfig": "51"}, {"size": 1375, "mtime": 1753424805039, "results": "86", "hashOfConfig": "51"}, {"size": 495, "mtime": 1753418795255, "results": "87", "hashOfConfig": "51"}, {"size": 435, "mtime": 1753428387847, "results": "88", "hashOfConfig": "51"}, {"size": 166, "mtime": 1753419075615, "results": "89", "hashOfConfig": "51"}, {"size": 296, "mtime": 1753418781941, "results": "90", "hashOfConfig": "51"}, {"size": 964, "mtime": 1753456634130, "results": "91", "hashOfConfig": "51"}, {"size": 526, "mtime": 1753458340677, "results": "92", "hashOfConfig": "51"}, {"size": 1642, "mtime": 1753467846793, "results": "93", "hashOfConfig": "51"}, {"size": 14656, "mtime": 1753458487964, "results": "94", "hashOfConfig": "51"}, {"size": 11177, "mtime": 1753493628789, "results": "95", "hashOfConfig": "51"}, {"size": 2600, "mtime": 1753467778607, "results": "96", "hashOfConfig": "51"}, {"size": 8100, "mtime": 1753469992843, "results": "97", "hashOfConfig": "51"}, {"size": 2688, "mtime": 1753465260568, "results": "98", "hashOfConfig": "51"}, {"size": 1818, "mtime": 1753464734284, "results": "99", "hashOfConfig": "51"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1pczg4f", {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\AppSidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\SupabaseSignIn.tsx", ["247"], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-183.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-356.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-581.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\debug\\SupabaseDebug.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\info-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\layout\\ConditionalLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\LoginDemo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\logo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\notification-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\PortfolioBalance.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ThemeToggle.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar-upload.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\sign-in-flow-1.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\user-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\ProfileContext.tsx", ["248", "249"], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\SidebarContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useAuth.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useLocalStorage.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useProfile.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useTheme.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\calculator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\positions-header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\positions-table.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\positions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\select.tsx", [], [], {"ruleId": "250", "severity": 1, "message": "251", "line": 159, "column": 5, "nodeType": null, "messageId": "252", "endLine": 159, "endColumn": 18}, {"ruleId": "253", "severity": 1, "message": "254", "line": 99, "column": 6, "nodeType": "255", "endLine": 99, "endColumn": 26, "suggestions": "256"}, {"ruleId": "253", "severity": 1, "message": "257", "line": 288, "column": 6, "nodeType": "255", "endLine": 288, "endColumn": 16, "suggestions": "258"}, "@typescript-eslint/no-unused-vars", "'signInWithOTP' is assigned a value but never used.", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'profile'. Either include it or remove the dependency array.", "ArrayExpression", ["259"], "React Hook useEffect has a missing dependency: 'user'. Either include it or remove the dependency array.", ["260"], {"desc": "261", "fix": "262"}, {"desc": "263", "fix": "264"}, "Update the dependencies array to be: [user, profile, isCacheValid]", {"range": "265", "text": "266"}, "Update the dependencies array to be: [user, user.id]", {"range": "267", "text": "268"}, [3142, 3162], "[user, profile, isCacheValid]", [8696, 8706], "[user, user.id]"]
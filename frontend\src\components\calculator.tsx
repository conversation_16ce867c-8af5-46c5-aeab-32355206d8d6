"use client"

import * as React from "react"
import Image from "next/image"
import { Button } from "@/components/ui/button"

interface CalculatorProps {
  className?: string
}

export default function Calculator({ className }: CalculatorProps) {
  const [activeTab, setActiveTab] = React.useState("head-to-head")
  const [activeStandingsSubTab, setActiveStandingsSubTab] = React.useState("standings")
  const [activeStandingsSubSubTab, setActiveStandingsSubSubTab] = React.useState("overall")

  return (
    <div className={`bg-background flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="px-6 py-3 border-b border-border">
        <h2 className="text-base font-medium text-foreground">
          Estatísticas
        </h2>
      </div>

      {/* Abas principais */}
      <div className="px-6 py-3 border-b border-border">
        <div className="flex gap-1">
          <Button
            size="sm"
            variant={activeTab === "head-to-head" ? "default" : "ghost"}
            onClick={() => setActiveTab("head-to-head")}
            className="h-7 px-3 text-xs"
          >
            Head-to-head
          </Button>
          <Button
            size="sm"
            variant={activeTab === "standings" ? "default" : "ghost"}
            onClick={() => setActiveTab("standings")}
            className="h-7 px-3 text-xs"
          >
            Standings
          </Button>
          <Button
            size="sm"
            variant={activeTab === "last-results" ? "default" : "ghost"}
            onClick={() => setActiveTab("last-results")}
            className="h-7 px-3 text-xs"
          >
            Last results
          </Button>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 p-6 space-y-6">
        {activeTab === "head-to-head" && (
          <div className="space-y-4">
            <div className="text-center space-y-2">
              <div className="flex items-center justify-between text-sm font-medium">
                <span className="text-foreground">Real Madrid</span>
                <span className="text-muted-foreground">vs</span>
                <span className="text-foreground">Inter de Milão</span>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between text-xs">
                <span className="text-foreground">8 vitórias</span>
                <span className="text-muted-foreground">4 empates</span>
                <span className="text-foreground">6 vitórias</span>
              </div>

              <div className="space-y-2">
                <div className="flex h-2 bg-muted rounded-full overflow-hidden">
                  <div className="bg-green-500 flex-[8]"></div>
                  <div className="bg-neutral-500 flex-[4]"></div>
                  <div className="bg-red-500 flex-[6]"></div>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>44.4%</span>
                  <span>22.2%</span>
                  <span>33.3%</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-sm font-medium text-foreground">Últimos confrontos</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-muted/20 rounded border border-border">
                  <div className="flex items-center gap-2">
                    <Image src="/real madrid.png" alt="Real Madrid" width={16} height={16} className="rounded" />
                    <span className="text-xs text-foreground">2</span>
                  </div>
                  <span className="text-xs text-muted-foreground">-</span>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-foreground">1</span>
                    <Image src="/inter shield.png" alt="Inter" width={16} height={16} className="rounded" />
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted/20 rounded border border-border">
                  <div className="flex items-center gap-2">
                    <Image src="/inter shield.png" alt="Inter" width={16} height={16} className="rounded" />
                    <span className="text-xs text-foreground">1</span>
                  </div>
                  <span className="text-xs text-muted-foreground">-</span>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-foreground">0</span>
                    <Image src="/real madrid.png" alt="Real Madrid" width={16} height={16} className="rounded" />
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted/20 rounded border border-border">
                  <div className="flex items-center gap-2">
                    <Image src="/real madrid.png" alt="Real Madrid" width={16} height={16} className="rounded" />
                    <span className="text-xs text-foreground">3</span>
                  </div>
                  <span className="text-xs text-muted-foreground">-</span>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-foreground">2</span>
                    <Image src="/inter shield.png" alt="Inter" width={16} height={16} className="rounded" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "standings" && (
          <div className="space-y-4">
            {/* Sub-abas principais */}
            <div className="flex gap-1 border-b border-border pb-3">
              <Button
                size="sm"
                variant={activeStandingsSubTab === "standings" ? "default" : "ghost"}
                onClick={() => setActiveStandingsSubTab("standings")}
                className="h-7 px-3 text-xs"
              >
                Standings
              </Button>
              <Button
                size="sm"
                variant={activeStandingsSubTab === "form" ? "default" : "ghost"}
                onClick={() => setActiveStandingsSubTab("form")}
                className="h-7 px-3 text-xs"
              >
                Form
              </Button>
              <Button
                size="sm"
                variant={activeStandingsSubTab === "over-under" ? "default" : "ghost"}
                onClick={() => setActiveStandingsSubTab("over-under")}
                className="h-7 px-3 text-xs"
              >
                Over/Under
              </Button>
              <Button
                size="sm"
                variant={activeStandingsSubTab === "ht-ft" ? "default" : "ghost"}
                onClick={() => setActiveStandingsSubTab("ht-ft")}
                className="h-7 px-3 text-xs"
              >
                HT/FT
              </Button>
              <Button
                size="sm"
                variant={activeStandingsSubTab === "top-scorers" ? "default" : "ghost"}
                onClick={() => setActiveStandingsSubTab("top-scorers")}
                className="h-7 px-3 text-xs"
              >
                Top Scorers
              </Button>
            </div>

            {/* Sub-sub-abas (para todas exceto Top Scorers) */}
            {activeStandingsSubTab !== "top-scorers" && (
              <div className="flex gap-1 pb-3">
                <Button
                  size="sm"
                  variant={activeStandingsSubSubTab === "overall" ? "secondary" : "ghost"}
                  onClick={() => setActiveStandingsSubSubTab("overall")}
                  className="h-6 px-2 text-xs"
                >
                  Overall
                </Button>
                <Button
                  size="sm"
                  variant={activeStandingsSubSubTab === "home" ? "secondary" : "ghost"}
                  onClick={() => setActiveStandingsSubSubTab("home")}
                  className="h-6 px-2 text-xs"
                >
                  Home
                </Button>
                <Button
                  size="sm"
                  variant={activeStandingsSubSubTab === "away" ? "secondary" : "ghost"}
                  onClick={() => setActiveStandingsSubSubTab("away")}
                  className="h-6 px-2 text-xs"
                >
                  Away
                </Button>
              </div>
            )}

            {/* Conteúdo das sub-abas */}
            <div className="space-y-4">
              {activeStandingsSubTab === "standings" && (
                <div className="text-center text-muted-foreground">
                  <p>Conteúdo de Standings - {activeStandingsSubSubTab}</p>
                  <p className="text-xs mt-2">Tabela de classificação será implementada aqui</p>
                </div>
              )}
              
              {activeStandingsSubTab === "form" && (
                <div className="text-center text-muted-foreground">
                  <p>Conteúdo de Form - {activeStandingsSubSubTab}</p>
                  <p className="text-xs mt-2">Forma dos times será implementada aqui</p>
                </div>
              )}
              
              {activeStandingsSubTab === "over-under" && (
                <div className="text-center text-muted-foreground">
                  <p>Conteúdo de Over/Under - {activeStandingsSubSubTab}</p>
                  <p className="text-xs mt-2">Estatísticas de Over/Under serão implementadas aqui</p>
                </div>
              )}
              
              {activeStandingsSubTab === "ht-ft" && (
                <div className="text-center text-muted-foreground">
                  <p>Conteúdo de HT/FT - {activeStandingsSubSubTab}</p>
                  <p className="text-xs mt-2">Estatísticas de Half Time/Full Time serão implementadas aqui</p>
                </div>
              )}
              
              {activeStandingsSubTab === "top-scorers" && (
                <div className="text-center text-muted-foreground">
                  <p>Conteúdo de Top Scorers</p>
                  <p className="text-xs mt-2">Lista de artilheiros será implementada aqui</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === "last-results" && (
          <div className="text-center text-muted-foreground">
            <p>Conteúdo de Last Results</p>
            <p className="text-xs mt-2">Últimos resultados serão implementados aqui</p>
          </div>
        )}
      </div>

      {/* Botões de ação */}
      <div className="p-6 border-t border-border">
        <div className="grid grid-cols-2 gap-3">
          <Button className="bg-primary text-primary-foreground">
            Calculate
          </Button>
          <Button variant="outline">
            Copy Strategy
          </Button>
        </div>
      </div>
    </div>
  )
}
